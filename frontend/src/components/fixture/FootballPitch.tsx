'use client';

import Image from 'next/image';
import { TeamLineup, MatchEvents, MatchStatistics, Player } from '@/types/fixture';
import { Fixture } from '@/lib/api';

interface FootballPitchProps {
  homeLineup: TeamLineup;
  awayLineup: TeamLineup;
  fixture: Fixture;
  events?: MatchEvents;
  statistics?: MatchStatistics;
}

export default function FootballPitch({
  homeLineup,
  awayLineup,
  fixture,
  events
}: FootballPitchProps) {

  // Convert lineup to formation structure
  const getFormationData = (lineup: TeamLineup, isHome: boolean) => {
    if (!lineup.startXI) return { gk: null, lines: [] };

    // Group players by formation line (row)
    const playersByRow: { [key: number]: Player[] } = {};
    let goalkeeper: Player | null = null;

    lineup.startXI.forEach((playerData) => {
      const player = playerData.player;

      if (player.pos === 'G') {
        goalkeeper = player;
      } else if (player.grid) {
        const row = parseInt(player.grid.split(':')[0], 10);
        if (!playersByRow[row]) playersByRow[row] = [];
        playersByRow[row].push(player);
      }
    });

    // Sort players within each row by column position
    Object.keys(playersByRow).forEach(row => {
      playersByRow[parseInt(row)].sort((a, b) => {
        const colA = a.grid ? parseInt(a.grid.split(':')[1], 10) : 0;
        const colB = b.grid ? parseInt(b.grid.split(':')[1], 10) : 0;
        return colA - colB;
      });
    });

    // Create formation lines (excluding goalkeeper)
    const lines = Object.keys(playersByRow)
      .map(row => parseInt(row))
      .filter(row => row > 1) // Exclude goalkeeper row
      .sort((a, b) => isHome ? a - b : b - a) // Home: GK->FW, Away: FW->GK
      .map(row => playersByRow[row]);

    return { gk: goalkeeper, lines };
  };



  // Get player goals from events
  const getPlayerGoals = (playerId: number, teamId: number): number => {
    if (!events) return 0;
    
    return events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Goal' &&
      !event.detail.toLowerCase().includes('own')
    ).length;
  };

  // Get player cards from events
  const getPlayerCards = (playerId: number, teamId: number): { yellow: number; red: number } => {
    if (!events) return { yellow: 0, red: 0 };
    
    const cardEvents = events.filter(event => 
      event.player?.id === playerId && 
      event.team.id === teamId && 
      event.type === 'Card'
    );
    
    const yellow = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('yellow')
    ).length;
    
    const red = cardEvents.filter(event => 
      event.detail.toLowerCase().includes('red')
    ).length;
    
    return { yellow, red };
  };



  // Get formation data for both teams
  const homeFormation = getFormationData(homeLineup, true);
  const awayFormation = getFormationData(awayLineup, false);

  // Render a player
  const renderPlayer = (player: Player, isHome: boolean) => {
    const goals = getPlayerGoals(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);
    const cards = getPlayerCards(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);

    // Default colors
    const defaultColor = isHome ? '#3b82f6' : '#ef4444';
    const gkColor = isHome ? '#059669' : '#dc2626';
    const playerColor = player.pos === 'G' ? gkColor : defaultColor;

    return (
      <div key={player.id} className="flex flex-col items-center mx-1">
        {/* Player Circle */}
        <div
          className="w-12 h-12 rounded-full border-2 border-white flex items-center justify-center text-white font-bold text-sm relative shadow-lg"
          style={{ backgroundColor: playerColor }}
        >
          <span>{player.number || '?'}</span>

          {/* Goal indicator */}
          {goals > 0 && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center text-xs text-white font-bold">
              {goals}
            </div>
          )}

          {/* Card indicators */}
          {cards.yellow > 0 && (
            <div className="absolute -top-1 -left-1 w-3 h-4 bg-yellow-400 rounded-sm"></div>
          )}
          {cards.red > 0 && (
            <div className="absolute -top-1 -left-1 w-3 h-4 bg-red-500 rounded-sm"></div>
          )}
        </div>

        {/* Player Name */}
        <div className="mt-1 text-xs text-center text-white font-medium max-w-20 truncate bg-black bg-opacity-70 px-2 py-0.5 rounded">
          {player.name.split(' ').pop()}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-card rounded-lg border border-border p-4 md:p-6">
      {/* Team Headers */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-3">
          <Image
            src={homeLineup.team.logo}
            alt={homeLineup.team.name}
            width={32}
            height={32}
            className="w-8 h-8"
          />
          <div>
            <h4 className="font-semibold">{homeLineup.team.name}</h4>
            {homeLineup.formation && (
              <p className="text-sm text-muted-foreground">{homeLineup.formation}</p>
            )}
          </div>
        </div>

        <div className="text-center">
          <h4 className="font-semibold text-lg">Lineups</h4>
          <p className="text-sm text-muted-foreground">Formation View</p>
        </div>

        <div className="flex items-center space-x-3">
          <div>
            <h4 className="font-semibold text-right">{awayLineup.team.name}</h4>
            {awayLineup.formation && (
              <p className="text-sm text-muted-foreground text-right">{awayLineup.formation}</p>
            )}
          </div>
          <Image
            src={awayLineup.team.logo}
            alt={awayLineup.team.name}
            width={32}
            height={32}
            className="w-8 h-8"
          />
        </div>
      </div>

      {/* Football Pitch */}
      <div className="w-full max-w-6xl mx-auto">
        <div
          className="relative w-full bg-green-500 rounded-lg shadow-lg overflow-hidden"
          style={{ paddingBottom: '60%' }} // 5:3 aspect ratio
        >
          {/* Pitch Markings */}
          <svg className="absolute inset-0 w-full h-full">
            {/* Outer boundary */}
            <rect x="2" y="2" width="calc(100% - 4px)" height="calc(100% - 4px)" fill="none" stroke="white" strokeWidth="2"/>

            {/* Center Line */}
            <line x1="50%" y1="0" x2="50%" y2="100%" stroke="white" strokeWidth="2"/>

            {/* Center Circle */}
            <circle cx="50%" cy="50%" r="12%" fill="none" stroke="white" strokeWidth="2"/>
            <circle cx="50%" cy="50%" r="1%" fill="white"/>

            {/* Goal Areas */}
            <rect x="0" y="35%" width="8%" height="30%" fill="none" stroke="white" strokeWidth="2"/>
            <rect x="92%" y="35%" width="8%" height="30%" fill="none" stroke="white" strokeWidth="2"/>

            {/* Penalty Areas */}
            <rect x="0" y="25%" width="15%" height="50%" fill="none" stroke="white" strokeWidth="2"/>
            <rect x="85%" y="25%" width="15%" height="50%" fill="none" stroke="white" strokeWidth="2"/>

            {/* Penalty spots */}
            <circle cx="10%" cy="50%" r="1%" fill="white"/>
            <circle cx="90%" cy="50%" r="1%" fill="white"/>
          </svg>

          {/* Formation Layout */}
          <div className="absolute inset-0">
            {/* Home Team (Left Half) */}
            <div className="absolute left-0 top-0 w-1/2 h-full">
              {/* Goalkeeper */}
              {homeFormation.gk && (
                <div className="absolute" style={{ left: '8%', top: '50%', transform: 'translateY(-50%)' }}>
                  {renderPlayer(homeFormation.gk, true)}
                </div>
              )}

              {/* Formation Lines */}
              {homeFormation.lines.map((line, lineIndex) => {
                const linePositions = [20, 40, 65, 85]; // Percentage positions from left goal
                const xPosition = linePositions[lineIndex] || 50;

                return (
                  <div key={lineIndex} className="absolute w-full" style={{ left: `${xPosition}%`, transform: 'translateX(-50%)' }}>
                    {line.map((player: Player, playerIndex) => {
                      // Spread players vertically across the pitch
                      const playersCount = line.length;
                      const spacing = 60; // 60% of pitch height for player spread
                      const startY = 20; // Start at 20% from top
                      const yPosition = playersCount === 1
                        ? 50 // Center single player
                        : startY + (playerIndex * spacing / (playersCount - 1));

                      return (
                        <div
                          key={player.id}
                          className="absolute"
                          style={{
                            top: `${yPosition}%`,
                            left: '0%',
                            transform: 'translate(-50%, -50%)'
                          }}
                        >
                          {renderPlayer(player, true)}
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>

            {/* Away Team (Right Half) */}
            <div className="absolute right-0 top-0 w-1/2 h-full">
              {/* Goalkeeper */}
              {awayFormation.gk && (
                <div className="absolute" style={{ right: '8%', top: '50%', transform: 'translateY(-50%)' }}>
                  {renderPlayer(awayFormation.gk, false)}
                </div>
              )}

              {/* Formation Lines */}
              {awayFormation.lines.map((line, lineIndex) => {
                const linePositions = [20, 40, 65, 85]; // Percentage positions from right goal
                const xPosition = linePositions[lineIndex] || 50;

                return (
                  <div key={lineIndex} className="absolute w-full" style={{ right: `${xPosition}%`, transform: 'translateX(50%)' }}>
                    {line.map((player: Player, playerIndex) => {
                      // Spread players vertically across the pitch
                      const playersCount = line.length;
                      const spacing = 60; // 60% of pitch height for player spread
                      const startY = 20; // Start at 20% from top
                      const yPosition = playersCount === 1
                        ? 50 // Center single player
                        : startY + (playerIndex * spacing / (playersCount - 1));

                      return (
                        <div
                          key={player.id}
                          className="absolute"
                          style={{
                            top: `${yPosition}%`,
                            right: '0%',
                            transform: 'translate(50%, -50%)'
                          }}
                        >
                          {renderPlayer(player, false)}
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
